| name                         | title                        | level | facing   | categories      | description                                                                                                                                                                                                                                                 | detail                                                                                                                                                                                                                                                                                                                                                                                                                                                          | remediation                                                                                      | metadata                                                       | cache_key                                                                          |
| ---------------------------- | ---------------------------- | ----- | -------- | --------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------ | -------------------------------------------------------------- | ---------------------------------------------------------------------------------- |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.users\` has a row level security policy \`Users can view own profile\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                         | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"users","type":"table","schema":"public"}              | auth_rls_init_plan_public_users_Users can view own profile                         |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.users\` has a row level security policy \`Users can update own profile\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                       | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"users","type":"table","schema":"public"}              | auth_rls_init_plan_public_users_Users can update own profile                       |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.sessions\` has a row level security policy \`Users can view own sessions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                     | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"sessions","type":"table","schema":"public"}           | auth_rls_init_plan_public_sessions_Users can view own sessions                     |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.sessions\` has a row level security policy \`Users can delete own sessions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                   | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"sessions","type":"table","schema":"public"}           | auth_rls_init_plan_public_sessions_Users can delete own sessions                   |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.accounts\` has a row level security policy \`Users can view own accounts\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                     | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"accounts","type":"table","schema":"public"}           | auth_rls_init_plan_public_accounts_Users can view own accounts                     |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.subscriptions\` has a row level security policy \`Users can view own subscriptions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.           | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"subscriptions","type":"table","schema":"public"}      | auth_rls_init_plan_public_subscriptions_Users can view own subscriptions           |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.one_time_purchases\` has a row level security policy \`Users can view own purchases\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.          | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"one_time_purchases","type":"table","schema":"public"} | auth_rls_init_plan_public_one_time_purchases_Users can view own purchases          |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.users\` has a row level security policy \`Service role can manage all users\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.                  | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"users","type":"table","schema":"public"}              | auth_rls_init_plan_public_users_Service role can manage all users                  |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.sessions\` has a row level security policy \`Service role can manage all sessions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.            | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"sessions","type":"table","schema":"public"}           | auth_rls_init_plan_public_sessions_Service role can manage all sessions            |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.accounts\` has a row level security policy \`Service role can manage all accounts\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.            | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"accounts","type":"table","schema":"public"}           | auth_rls_init_plan_public_accounts_Service role can manage all accounts            |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.subscriptions\` has a row level security policy \`Service role can manage all subscriptions\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.  | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"subscriptions","type":"table","schema":"public"}      | auth_rls_init_plan_public_subscriptions_Service role can manage all subscriptions  |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.one_time_purchases\` has a row level security policy \`Service role can manage all purchases\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info. | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"one_time_purchases","type":"table","schema":"public"} | auth_rls_init_plan_public_one_time_purchases_Service role can manage all purchases |
| auth_rls_initplan            | Auth RLS Initialization Plan | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if calls to \`current_setting()\` and \`auth.<function>()\` in RLS policies are being unnecessarily re-evaluated for each row                                                                                                                       | Table \`public.verifications\` has a row level security policy \`Service role can manage all verifications\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \`auth.<function>()\` with \`(select auth.<function>())\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.  | https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan            | {"name":"verifications","type":"table","schema":"public"}      | auth_rls_init_plan_public_verifications_Service role can manage all verifications  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage all accounts","Users can view own accounts"}\`                                                                                                                                                                                                                                                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_anon_SELECT                           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage all accounts","Users can view own accounts"}\`                                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_authenticated_SELECT                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage all accounts","Users can view own accounts"}\`                                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_authenticator_SELECT                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.accounts\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage all accounts","Users can view own accounts"}\`                                                                                                                                                                                                                                                       | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"accounts","type":"table","schema":"public"}           | multiple_permissive_policies_public_accounts_dashboard_user_SELECT                 |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage all purchases","Users can view own purchases"}\`                                                                                                                                                                                                                                                     | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_anon_SELECT                 |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage all purchases","Users can view own purchases"}\`                                                                                                                                                                                                                                            | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_authenticated_SELECT        |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage all purchases","Users can view own purchases"}\`                                                                                                                                                                                                                                            | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_authenticator_SELECT        |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.one_time_purchases\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage all purchases","Users can view own purchases"}\`                                                                                                                                                                                                                                           | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"one_time_purchases","type":"table","schema":"public"} | multiple_permissive_policies_public_one_time_purchases_dashboard_user_SELECT       |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`anon\` for action \`DELETE\`. Policies include \`{"Service role can manage all sessions","Users can delete own sessions"}\`                                                                                                                                                                                                                                                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_anon_DELETE                           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage all sessions","Users can view own sessions"}\`                                                                                                                                                                                                                                                                 | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_anon_SELECT                           |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticated\` for action \`DELETE\`. Policies include \`{"Service role can manage all sessions","Users can delete own sessions"}\`                                                                                                                                                                                                                                                      | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticated_DELETE                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage all sessions","Users can view own sessions"}\`                                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticated_SELECT                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticator\` for action \`DELETE\`. Policies include \`{"Service role can manage all sessions","Users can delete own sessions"}\`                                                                                                                                                                                                                                                      | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticator_DELETE                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage all sessions","Users can view own sessions"}\`                                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_authenticator_SELECT                  |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`dashboard_user\` for action \`DELETE\`. Policies include \`{"Service role can manage all sessions","Users can delete own sessions"}\`                                                                                                                                                                                                                                                     | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_dashboard_user_DELETE                 |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.sessions\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage all sessions","Users can view own sessions"}\`                                                                                                                                                                                                                                                       | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"sessions","type":"table","schema":"public"}           | multiple_permissive_policies_public_sessions_dashboard_user_SELECT                 |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage all subscriptions","Users can view own subscriptions"}\`                                                                                                                                                                                                                                                  | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_anon_SELECT                      |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage all subscriptions","Users can view own subscriptions"}\`                                                                                                                                                                                                                                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_authenticated_SELECT             |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage all subscriptions","Users can view own subscriptions"}\`                                                                                                                                                                                                                                         | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_authenticator_SELECT             |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.subscriptions\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage all subscriptions","Users can view own subscriptions"}\`                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"subscriptions","type":"table","schema":"public"}      | multiple_permissive_policies_public_subscriptions_dashboard_user_SELECT            |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`anon\` for action \`SELECT\`. Policies include \`{"Service role can manage all users","Users can view own profile"}\`                                                                                                                                                                                                                                                                        | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_anon_SELECT                              |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`anon\` for action \`UPDATE\`. Policies include \`{"Service role can manage all users","Users can update own profile"}\`                                                                                                                                                                                                                                                                      | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_anon_UPDATE                              |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticated\` for action \`SELECT\`. Policies include \`{"Service role can manage all users","Users can view own profile"}\`                                                                                                                                                                                                                                                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticated_SELECT                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticated\` for action \`UPDATE\`. Policies include \`{"Service role can manage all users","Users can update own profile"}\`                                                                                                                                                                                                                                                             | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticated_UPDATE                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticator\` for action \`SELECT\`. Policies include \`{"Service role can manage all users","Users can view own profile"}\`                                                                                                                                                                                                                                                               | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticator_SELECT                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`authenticator\` for action \`UPDATE\`. Policies include \`{"Service role can manage all users","Users can update own profile"}\`                                                                                                                                                                                                                                                             | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_authenticator_UPDATE                     |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`dashboard_user\` for action \`SELECT\`. Policies include \`{"Service role can manage all users","Users can view own profile"}\`                                                                                                                                                                                                                                                              | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_dashboard_user_SELECT                    |
| multiple_permissive_policies | Multiple Permissive Policies | WARN  | EXTERNAL | ["PERFORMANCE"] | Detects if multiple permissive row level security policies are present on a table for the same \`role\` and \`action\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query. | Table \`public.users\` has multiple permissive policies for role \`dashboard_user\` for action \`UPDATE\`. Policies include \`{"Service role can manage all users","Users can update own profile"}\`                                                                                                                                                                                                                                                            | https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies | {"name":"users","type":"table","schema":"public"}              | multiple_permissive_policies_public_users_dashboard_user_UPDATE                    |