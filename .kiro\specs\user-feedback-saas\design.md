# Design Document

## Overview

This document outlines the technical design for a comprehensive User Feedback Management SaaS platform. The system is built using Next.js 15 with App Router, Supabase for backend services, and follows a modern, scalable architecture pattern. The platform enables businesses to collect, organize, and manage customer feedback through multiple channels while providing real-time collaboration and stakeholder communication.

The design emphasizes modularity, real-time capabilities, security, and user experience across all components. The system supports multi-tenancy, role-based access control, and seamless integration with external services.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Dashboard]
        WIDGET[Embeddable Widget]
        MOBILE[Mobile Responsive]
    end
    
    subgraph "Application Layer"
        NEXTJS[Next.js App Router]
        API[API Routes]
        MIDDLEWARE[Auth Middleware]
        COMPONENTS[React Components]
    end
    
    subgraph "Backend Services"
        SUPABASE[Supabase]
        AUTH[Authentication]
        DB[PostgreSQL Database]
        REALTIME[Realtime Engine]
        STORAGE[File Storage]
    end
    
    subgraph "External Integrations"
        SLACK[Slack API]
        WEBHOOKS[Webhook Endpoints]
        EMAIL[Email Service]
    end
    
    WEB --> NEXTJS
    WIDGET --> API
    MOBILE --> NEXTJS
    
    NEXTJS --> SUPABASE
    API --> SUPABASE
    MIDDLEWARE --> AUTH
    
    SUPABASE --> SLACK
    SUPABASE --> WEBHOOKS
    SUPABASE --> EMAIL
```

### Technology Stack

- **Frontend**: Next.js 15 (App Router), React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Realtime, Storage)
- **Authentication**: Supabase Auth with JWT tokens
- **Real-time**: Supabase Realtime (WebSockets)
- **State Management**: React Context + useReducer for complex state
- **UI Components**: shadcn/ui component library
- **Deployment**: Vercel (frontend), Supabase (backend)
- **Monitoring**: Supabase Analytics + custom metrics

### Database Architecture

The system uses PostgreSQL with the following core schema design:

```mermaid
erDiagram
    organizations ||--o{ users : has
    organizations ||--o{ feedback_boards : owns
    organizations ||--o{ feature_campaigns : creates
    organizations ||--o{ integrations : configures
    
    users ||--o{ feedback_items : submits
    users ||--o{ comments : writes
    users ||--o{ votes : casts
    users ||--o{ user_profiles : has
    
    feedback_boards ||--o{ feedback_items : contains
    feedback_boards ||--o{ board_categories : has
    
    feedback_items ||--o{ comments : receives
    feedback_items ||--o{ votes : gets
    feedback_items ||--o{ roadmap_items : links_to
    
    feature_campaigns ||--o{ campaign_votes : receives
    
    roadmap_items ||--o{ roadmap_updates : has
    
    organizations {
        uuid id PK
        string name
        string slug
        jsonb settings
        timestamp created_at
        timestamp updated_at
    }
    
    users {
        uuid id PK
        uuid organization_id FK
        string email
        string name
        string role
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }
    
    feedback_boards {
        uuid id PK
        uuid organization_id FK
        string name
        string type
        boolean is_public
        string access_url
        jsonb settings
        timestamp created_at
        timestamp updated_at
    }
    
    feedback_items {
        uuid id PK
        uuid board_id FK
        uuid user_id FK
        string title
        text description
        string category
        string status
        integer vote_count
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }
    
    comments {
        uuid id PK
        uuid feedback_item_id FK
        uuid user_id FK
        text content
        uuid parent_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    votes {
        uuid id PK
        uuid feedback_item_id FK
        uuid user_id FK
        string vote_type
        timestamp created_at
    }
```

## Components and Interfaces

### Core Components

#### 1. Feedback Inbox Component
```typescript
interface FeedbackInboxProps {
  organizationId: string;
  filters: InboxFilters;
  onItemSelect: (item: FeedbackItem) => void;
}

interface InboxFilters {
  status: 'all' | 'unread' | 'assigned' | 'resolved';
  category: string[];
  assignee: string[];
  dateRange: DateRange;
  search: string;
}
```

#### 2. Feedback Board Component
```typescript
interface FeedbackBoardProps {
  boardId: string;
  isPublic: boolean;
  currentUser?: User;
  settings: BoardSettings;
}

interface BoardSettings {
  allowAnonymous: boolean;
  moderationEnabled: boolean;
  categories: Category[];
  votingEnabled: boolean;
  commentsEnabled: boolean;
}
```

#### 3. Embeddable Widget Component
```typescript
interface WidgetConfig {
  organizationId: string;
  boardId: string;
  position: 'left' | 'right';
  theme: 'light' | 'dark' | 'auto';
  customStyles?: CSSProperties;
  triggers: WidgetTrigger[];
}

interface WidgetTrigger {
  type: 'button' | 'modal' | 'slide';
  text: string;
  icon?: string;
}
```

#### 4. Real-time Notification System
```typescript
interface NotificationService {
  subscribe(channels: string[]): void;
  unsubscribe(channels: string[]): void;
  sendNotification(notification: Notification): Promise<void>;
  markAsRead(notificationId: string): Promise<void>;
}

interface Notification {
  id: string;
  type: 'feedback_submitted' | 'vote_received' | 'comment_added' | 'status_changed';
  title: string;
  message: string;
  data: Record<string, any>;
  recipients: string[];
  channels: ('email' | 'slack' | 'in_app')[];
}
```

### API Interfaces

#### 1. Feedback Management API
```typescript
// GET /api/feedback
interface GetFeedbackParams {
  boardId?: string;
  organizationId: string;
  status?: FeedbackStatus;
  category?: string;
  limit?: number;
  offset?: number;
  search?: string;
}

// POST /api/feedback
interface CreateFeedbackRequest {
  boardId: string;
  title: string;
  description: string;
  category: string;
  userInfo?: {
    name?: string;
    email?: string;
  };
  metadata?: Record<string, any>;
}

// PUT /api/feedback/[id]
interface UpdateFeedbackRequest {
  title?: string;
  description?: string;
  status?: FeedbackStatus;
  category?: string;
  assigneeId?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}
```

#### 2. Voting System API
```typescript
// POST /api/feedback/[id]/vote
interface VoteRequest {
  type: 'upvote' | 'downvote';
  userId?: string; // Optional for anonymous voting
}

// GET /api/feedback/[id]/votes
interface VoteResponse {
  upvotes: number;
  downvotes: number;
  userVote?: 'upvote' | 'downvote';
  topVoters: {
    userId: string;
    name: string;
    voteType: string;
    timestamp: string;
  }[];
}
```

#### 3. Feature Campaign API
```typescript
// POST /api/campaigns
interface CreateCampaignRequest {
  title: string;
  description: string;
  features: FeatureConcept[];
  settings: CampaignSettings;
  duration: number; // in days
}

interface FeatureConcept {
  id: string;
  title: string;
  description: string;
  mockups?: string[]; // URLs to mockup images
  estimatedEffort: 'small' | 'medium' | 'large';
}

interface CampaignSettings {
  allowAnonymous: boolean;
  requireEmail: boolean;
  maxVotesPerUser: number;
  showResults: boolean;
}
```

## Data Models

### Core Data Models

#### 1. Organization Model
```typescript
interface Organization {
  id: string;
  name: string;
  slug: string;
  settings: {
    branding: {
      logo?: string;
      primaryColor: string;
      secondaryColor: string;
    };
    features: {
      anonymousFeedback: boolean;
      moderationRequired: boolean;
      votingEnabled: boolean;
      roadmapPublic: boolean;
    };
    integrations: {
      slack?: SlackIntegration;
      webhooks?: WebhookConfig[];
    };
  };
  subscription: {
    plan: 'free' | 'pro' | 'enterprise';
    status: 'active' | 'cancelled' | 'past_due';
    limits: {
      boards: number;
      feedback: number;
      users: number;
    };
  };
  createdAt: string;
  updatedAt: string;
}
```

#### 2. Feedback Item Model
```typescript
interface FeedbackItem {
  id: string;
  boardId: string;
  userId?: string;
  title: string;
  description: string;
  category: string;
  status: 'submitted' | 'under_review' | 'planned' | 'in_progress' | 'completed' | 'rejected';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  voteCount: number;
  commentCount: number;
  tags: string[];
  assigneeId?: string;
  roadmapItemId?: string;
  metadata: {
    source: 'web' | 'widget' | 'api' | 'email';
    userAgent?: string;
    referrer?: string;
    customFields?: Record<string, any>;
  };
  createdAt: string;
  updatedAt: string;
}
```

#### 3. User Profile Model
```typescript
interface UserProfile {
  id: string;
  organizationId: string;
  email: string;
  name?: string;
  avatar?: string;
  role: 'admin' | 'moderator' | 'member' | 'viewer';
  permissions: Permission[];
  preferences: {
    notifications: {
      email: boolean;
      slack: boolean;
      inApp: boolean;
    };
    theme: 'light' | 'dark' | 'auto';
    language: string;
  };
  stats: {
    feedbackSubmitted: number;
    votesGiven: number;
    commentsPosted: number;
  };
  lastActiveAt: string;
  createdAt: string;
  updatedAt: string;
}
```

#### 4. Real-time Event Model
```typescript
interface RealtimeEvent {
  type: 'feedback_created' | 'feedback_updated' | 'vote_added' | 'comment_added' | 'status_changed';
  payload: {
    feedbackId: string;
    boardId: string;
    organizationId: string;
    userId?: string;
    data: Record<string, any>;
    timestamp: string;
  };
  channels: string[]; // Which channels should receive this event
}
```

## Error Handling

### Error Response Format
```typescript
interface APIError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
  status: number;
}

// Common error codes
enum ErrorCodes {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  BOARD_NOT_FOUND = 'BOARD_NOT_FOUND',
  FEEDBACK_NOT_FOUND = 'FEEDBACK_NOT_FOUND',
  DUPLICATE_VOTE = 'DUPLICATE_VOTE',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED'
}
```

### Error Handling Strategy
- **Client-side**: React Error Boundaries for component-level errors
- **API Routes**: Centralized error handler middleware
- **Database**: Transaction rollbacks and constraint validation
- **Real-time**: Connection retry logic with exponential backoff
- **External APIs**: Circuit breaker pattern for Slack/webhook failures

## Testing Strategy

### Testing Pyramid

#### 1. Unit Tests (70%)
- **Components**: React Testing Library for UI components
- **Utilities**: Jest for helper functions and utilities
- **API Routes**: Supertest for endpoint testing
- **Database**: Mock Supabase client for data layer tests

#### 2. Integration Tests (20%)
- **API Integration**: Test complete request/response cycles
- **Database Integration**: Test with real Supabase instance
- **Real-time Features**: Test WebSocket connections and events
- **Authentication Flow**: Test login/logout and session management

#### 3. End-to-End Tests (10%)
- **User Journeys**: Playwright for critical user flows
- **Cross-browser**: Test on Chrome, Firefox, Safari
- **Mobile Responsive**: Test on various device sizes
- **Widget Integration**: Test embeddable widget on external sites

### Test Data Management
```typescript
// Test data factories
interface TestDataFactory {
  createOrganization(overrides?: Partial<Organization>): Organization;
  createUser(organizationId: string, overrides?: Partial<User>): User;
  createFeedbackBoard(organizationId: string, overrides?: Partial<FeedbackBoard>): FeedbackBoard;
  createFeedbackItem(boardId: string, overrides?: Partial<FeedbackItem>): FeedbackItem;
}

// Database seeding for tests
interface TestSeeder {
  seedBasicOrganization(): Promise<{
    organization: Organization;
    admin: User;
    board: FeedbackBoard;
    feedback: FeedbackItem[];
  }>;
  cleanupTestData(): Promise<void>;
}
```

### Performance Testing
- **Load Testing**: Artillery.js for API endpoint load testing
- **Real-time Performance**: Test WebSocket connection limits
- **Database Performance**: Query optimization and indexing validation
- **Widget Performance**: Test widget load times on external sites

## Security Considerations

### Authentication & Authorization
- **JWT Tokens**: Supabase Auth with secure token management
- **Row Level Security**: PostgreSQL RLS policies for data isolation
- **Role-based Access**: Granular permissions system
- **Session Management**: Secure session handling with refresh tokens

### Data Protection
- **Encryption**: Data encrypted at rest and in transit
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection**: Parameterized queries and ORM protection
- **XSS Protection**: Content Security Policy and input escaping
- **CSRF Protection**: CSRF tokens for state-changing operations

### API Security
- **Rate Limiting**: Per-user and per-IP rate limiting
- **API Keys**: Secure API key management for widget integration
- **CORS Configuration**: Proper CORS setup for widget embedding
- **Webhook Security**: HMAC signature verification for webhooks

### Privacy Compliance
- **GDPR Compliance**: Data export, deletion, and consent management
- **Data Retention**: Configurable data retention policies
- **Audit Logging**: Comprehensive audit trail for data access
- **Anonymization**: Option to anonymize user data while preserving feedback

## Performance Optimization

### Frontend Performance
- **Code Splitting**: Route-based and component-based code splitting
- **Image Optimization**: Next.js Image component with WebP support
- **Caching Strategy**: SWR for client-side caching with revalidation
- **Bundle Analysis**: Regular bundle size monitoring and optimization

### Backend Performance
- **Database Indexing**: Optimized indexes for common query patterns
- **Connection Pooling**: Supabase connection pooling configuration
- **Query Optimization**: Efficient SQL queries with proper joins
- **Caching Layer**: Redis caching for frequently accessed data

### Real-time Performance
- **Connection Management**: Efficient WebSocket connection handling
- **Event Batching**: Batch similar events to reduce message frequency
- **Channel Optimization**: Targeted channel subscriptions
- **Presence Optimization**: Efficient user presence tracking

### Monitoring & Metrics
```typescript
interface PerformanceMetrics {
  api: {
    responseTime: number;
    errorRate: number;
    throughput: number;
  };
  realtime: {
    connectionCount: number;
    messageLatency: number;
    disconnectionRate: number;
  };
  database: {
    queryTime: number;
    connectionPool: number;
    slowQueries: Query[];
  };
  frontend: {
    pageLoadTime: number;
    firstContentfulPaint: number;
    cumulativeLayoutShift: number;
  };
}
```

This design provides a solid foundation for building a scalable, secure, and performant User Feedback Management SaaS platform that meets all the specified requirements while following modern development best practices.