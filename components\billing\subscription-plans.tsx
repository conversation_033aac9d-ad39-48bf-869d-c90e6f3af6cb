"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useEffect, useState } from "react";
import Image from "next/image";
import { Check, Star, Zap, Crown, Loader2 } from "lucide-react";
import { authClient } from "@/lib/auth-client";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  imageUrl?: string;
  status: string;
  billingType: string;
  billingPeriod: string;
  mode: string;
  object: string;
}

export function SubscriptionPlans() {
  const [products, setProducts] = useState<Product[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const { data: session } = authClient.useSession();

  const handleSubscribe = async (productId: string) => {
    try {
      setIsProcessing(productId);
      const response = await fetch(`/api/checkout?product_id=${productId}`);
      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }
      const data = await response.json();
      
      if (!data.checkoutUrl) {
        throw new Error("Invalid checkout URL received");
      }

      window.location.href = data.checkoutUrl;
    } catch (err) {
      console.error("Checkout error:", err);
      setError(err instanceof Error ? err.message : "Failed to start checkout");
    } finally {
      setIsProcessing(null);
    }
  };

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch("/api/products");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // Debug the response structure
      console.log("API Response:", data);

      if (!data.items || !Array.isArray(data.items)) {
        console.error("Invalid data format:", data);
        throw new Error("Invalid data format from API");
      }
      setProducts(data.items);
    } catch (err) {
      console.error("Fetch error:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch subscriptions");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const getPlanIcon = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('pro') || lowerName.includes('premium')) {
      return <Crown className="w-5 h-5 text-yellow-500" />;
    }
    if (lowerName.includes('enterprise') || lowerName.includes('business')) {
      return <Zap className="w-5 h-5 text-purple-500" />;
    }
    return <Star className="w-5 h-5 text-blue-500" />;
  };

  const getPlanBadge = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('pro') || lowerName.includes('premium')) {
      return <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Popular</Badge>;
    }
    if (lowerName.includes('enterprise') || lowerName.includes('business')) {
      return <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Enterprise</Badge>;
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-neutral-900 dark:text-white mb-2">
            Available Plans
          </h2>
          <p className="text-neutral-600 dark:text-neutral-400">
            Choose the perfect plan for your needs
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-neutral-200 dark:bg-neutral-800 rounded w-3/4"></div>
                <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-full"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="h-8 bg-neutral-200 dark:bg-neutral-800 rounded w-1/2"></div>
                  <div className="h-10 bg-neutral-200 dark:bg-neutral-800 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20">
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">Error: {error}</p>
            <Button
              onClick={fetchProducts}
              variant="outline"
              className="border-red-300 dark:border-red-700"
            >
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (products.length === 0) {
    return (
      <Card>
        <CardContent className="p-12">
          <div className="text-center">
            <Zap className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-200 mb-2">
              No Subscription Plans Available
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400">
              Subscription plans will appear here once they're configured.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-neutral-900 dark:text-white mb-2">
          Available Plans
        </h2>
        <p className="text-neutral-600 dark:text-neutral-400">
          Choose the perfect plan for your needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
          >
            <Card className="relative h-full hover:shadow-lg transition-all duration-200 group">
              {getPlanBadge(product.name) && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                  {getPlanBadge(product.name)}
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-center mb-3">
                  {getPlanIcon(product.name)}
                </div>
                <CardTitle className="text-xl font-semibold text-neutral-900 dark:text-white">
                  {product.name}
                </CardTitle>
                <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                  {product.description.replace(/[#*`]/g, "")}
                </p>
              </CardHeader>

              <CardContent className="space-y-6">
                {product.imageUrl && (
                  <div className="relative w-full aspect-video rounded-lg overflow-hidden">
                    <Image
                      src={product.imageUrl}
                      alt={product.name}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                )}

                <div className="text-center">
                  <div className="text-3xl font-bold text-neutral-900 dark:text-white">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: product.currency,
                      minimumFractionDigits: 0,
                    }).format(product.price / 100)}
                  </div>
                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                    {product.billingType === "recurring"
                      ? `per ${product.billingPeriod.replace("every-", "")}`
                      : "one-time payment"}
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Full access to all features</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>24/7 customer support</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Cancel anytime</span>
                  </div>
                </div>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => handleSubscribe(product.id)}
                        disabled={isProcessing === product.id}
                        className="w-full bg-gradient-to-r from-[#ffbe98] to-[#ff9a56] hover:from-[#ffbe98]/90 hover:to-[#ff9a56]/90 text-white font-medium"
                      >
                        {isProcessing === product.id ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                            Processing...
                          </>
                        ) : (
                          <>
                            Subscribe to {product.name}
                          </>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Start your subscription to {product.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
