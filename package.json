{"name": "placeholder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.3", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/supabase-js": "^2.52.0", "@tabler/icons-react": "^3.12.0", "axios": "^1.7.5", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "creem": "0.3.36", "framer-motion": "^12.23.9", "lucide-react": "^0.436.0", "next": "14.2.6", "next-themes": "^0.4.6", "next-view-transitions": "^0.3.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-wrap-balancer": "^1.1.1", "simplex-noise": "^4.0.3", "sonner": "^2.0.6", "tabler-icons-react": "^1.56.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}