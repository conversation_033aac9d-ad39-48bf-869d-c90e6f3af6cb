import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";
import { subscriptionOperations } from "@/lib/database";

/**
 * GET /api/gdpr/export
 * 
 * GDPR Article 20 - Right to Data Portability
 * Export all user data in machine-readable format (JSON)
 * 
 * Security Requirements:
 * - User must be authenticated
 * - Returns comprehensive data export
 * - Logs the export request for audit purposes
 */
export async function GET(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    console.log(`Data export requested by user: ${userId}`);

    // Collect all user data from different tables
    const [
      userProfile,
      userSessions,
      userAccounts,
      userSubscriptions,
      userConsents,
      userAuditLogs,
      userDeletionRequests,
      userCookieConsents
    ] = await Promise.all([
      // Basic user profile
      supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', userId)
        .single(),

      // User sessions
      supabaseAdmin
        .from('sessions')
        .select('*')
        .eq('user_id', userId),

      // OAuth accounts
      supabaseAdmin
        .from('accounts')
        .select('*')
        .eq('user_id', userId),

      // Subscriptions
      subscriptionOperations.findByUserId(userId),

      // GDPR consents
      supabaseAdmin
        .from('user_consents')
        .select('*')
        .eq('user_id', userId),

      // Audit logs
      supabaseAdmin
        .from('audit_logs')
        .select('*')
        .eq('user_id', userId),

      // Deletion requests
      supabaseAdmin
        .from('deletion_requests')
        .select('*')
        .eq('user_id', userId),

      // Cookie consents
      supabaseAdmin
        .from('cookie_consents')
        .select('*')
        .eq('user_id', userId)
    ]);

    // Prepare comprehensive data export
    const exportData = {
      export_info: {
        export_date: new Date().toISOString(),
        user_id: userId,
        export_type: 'complete_data_export',
        gdpr_article: 'Article 20 - Right to Data Portability'
      },
      personal_data: {
        profile: userProfile.data,
        sessions: userSessions.data || [],
        oauth_accounts: userAccounts.data || [],
        subscriptions: userSubscriptions || [],
        gdpr_consents: userConsents.data || [],
        audit_logs: userAuditLogs.data || [],
        deletion_requests: userDeletionRequests.data || [],
        cookie_consents: userCookieConsents.data || []
      },
      data_summary: {
        total_sessions: userSessions.data?.length || 0,
        total_oauth_accounts: userAccounts.data?.length || 0,
        total_subscriptions: userSubscriptions?.length || 0,
        total_consents: userConsents.data?.length || 0,
        total_audit_logs: userAuditLogs.data?.length || 0,
        total_deletion_requests: userDeletionRequests.data?.length || 0,
        total_cookie_consents: userCookieConsents.data?.length || 0
      }
    };

    // Log the data export request for audit purposes
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'data_export',
        resource: 'complete_user_data',
        details: {
          export_size_kb: Math.round(JSON.stringify(exportData).length / 1024),
          tables_exported: [
            'users', 'sessions', 'accounts', 'subscriptions', 
            'user_consents', 'audit_logs', 'deletion_requests', 'cookie_consents'
          ]
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    console.log(`Data export completed for user: ${userId}`);

    // Return the data export with appropriate headers
    return NextResponse.json(exportData, {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="data-export-${userId}-${new Date().toISOString().split('T')[0]}.json"`
      }
    });

  } catch (error: any) {
    console.error('Data export error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/gdpr/export
 * 
 * Request specific data export (partial export)
 * Allows users to export specific data categories
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { categories = [] } = body; // Array of data categories to export

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const exportData: any = {
      export_info: {
        export_date: new Date().toISOString(),
        user_id: userId,
        export_type: 'partial_data_export',
        categories_requested: categories,
        gdpr_article: 'Article 20 - Right to Data Portability'
      },
      personal_data: {}
    };

    // Export specific categories based on request
    if (categories.includes('profile') || categories.length === 0) {
      const userProfile = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      exportData.personal_data.profile = userProfile.data;
    }

    if (categories.includes('subscriptions') || categories.length === 0) {
      const userSubscriptions = await subscriptionOperations.findByUserId(userId);
      exportData.personal_data.subscriptions = userSubscriptions;
    }

    if (categories.includes('consents') || categories.length === 0) {
      const userConsents = await supabaseAdmin
        .from('user_consents')
        .select('*')
        .eq('user_id', userId);
      exportData.personal_data.consents = userConsents.data;
    }

    // Log the partial export request
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'partial_data_export',
        resource: 'user_data',
        details: {
          categories_exported: categories,
          export_size_kb: Math.round(JSON.stringify(exportData).length / 1024)
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    return NextResponse.json(exportData);

  } catch (error: any) {
    console.error('Partial data export error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
