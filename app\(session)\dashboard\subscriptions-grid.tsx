"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useEffect, useState } from "react";
import Image from "next/image";

interface Subscription {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  imageUrl?: string;
  status: string;
  billingType: string;
  billingPeriod: string;
  mode: string;
  object: string;
}

export function SubscriptionsGrid() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);

  const handleCheckout = async (subscriptionId: string) => {
    try {
      setIsProcessing(subscriptionId);
      const response = await fetch(`/api/checkout?product_id=${subscriptionId}`);
      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }
      const data = await response.json();

      if (!data.checkoutUrl) {
        throw new Error("Invalid checkout URL received");
      }

      window.location.href = data.checkoutUrl;
    } catch (err) {
      console.error("Checkout error:", err);
      setError(err instanceof Error ? err.message : "Failed to process checkout");
    } finally {
      setIsProcessing(null);
    }
  };

  useEffect(() => {
    async function fetchSubscriptions() {
      try {
        const response = await fetch("/api/products");
        if (!response.ok) {
          throw new Error("Failed to fetch subscriptions");
        }
        const data = await response.json();

        // Debug the response structure
        console.log("API Response:", data);

        if (!data.items || !Array.isArray(data.items)) {
          console.error("Invalid data format:", data);
          throw new Error("Invalid data format from API");
        }
        setSubscriptions(data.items);
      } catch (err) {
        console.error("Fetch error:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch subscriptions",
        );
      } finally {
        setIsLoading(false);
      }
    }

    fetchSubscriptions();
  }, []);

  if (error) {
    return (
      <Card className="p-4 md:p-6 border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          className="mt-4"
        >
          Try again
        </Button>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-3 md:gap-4">
        {[...Array(4)].map((_, i) => (
          <Card
            key={i}
            className="p-4 md:p-6 animate-pulse"
          >
            <div className="space-y-4">
              <div className="h-6 bg-neutral-200 dark:bg-neutral-800 rounded w-3/4"></div>
              <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-full"></div>
              <div className="flex items-center justify-between">
                <div className="h-6 bg-neutral-200 dark:bg-neutral-800 rounded w-1/4"></div>
                <div className="h-9 bg-neutral-200 dark:bg-neutral-800 rounded w-1/3"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-3 md:gap-4">
      {subscriptions.map((subscription) => (
        <motion.div
          key={subscription.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="group"
        >
          <Card className="p-4 md:p-6 hover:shadow-lg transition-all h-full group-hover:border-[#ffbe98]/20">
            <div className="flex flex-col h-full space-y-4">
              {subscription.imageUrl && (
                <div className="relative w-full aspect-video rounded-lg overflow-hidden flex-none">
                  <Image
                    src={subscription.imageUrl}
                    alt={subscription.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
              )}
              <div className="flex-1 space-y-4">
                <div className="font-semibold text-neutral-900 dark:text-neutral-200">{subscription.name}</div>
                <p className="text-neutral-600 dark:text-neutral-400 text-sm line-clamp-2">
                  {subscription.description.replace(/[#*`]/g, "")}
                </p>
              </div>
              <div className="flex items-center justify-between pt-4 border-t border-neutral-200 dark:border-neutral-800">
                <div className="space-y-1">
                  <span className="text-neutral-900 dark:text-neutral-200 font-semibold">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: subscription.currency,
                      minimumFractionDigits: 2,
                    }).format(subscription.price / 100)}
                  </span>
                  <div className="text-xs text-neutral-500 dark:text-neutral-400">
                    {subscription.billingType === "recurring"
                      ? `Billed ${subscription.billingPeriod.replace("every-", "")}`
                      : "One-time payment"}
                  </div>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-neutral-800 text-neutral-200 hover:bg-neutral-800/20"
                        onClick={() => handleCheckout(subscription.id)}
                        disabled={isProcessing === subscription.id}
                      >
                        {isProcessing === subscription.id ? (
                          "Processing..."
                        ) : subscription.billingType === "recurring" ? (
                          "Subscribe"
                        ) : (
                          "Buy Now"
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Subscribe to {subscription.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}
