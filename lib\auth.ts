import { betterAuth } from "better-auth";
import { supabaseAdapter } from "./supabase-adapter";

/**
 * Better Auth configuration using pure Supabase adapter
 * This uses a custom Supabase adapter that integrates directly with Supabase client
 * instead of using DATABASE_URL or PostgreSQL connection strings
 */
export const auth = betterAuth({
  database: supabaseAdapter({
    debugLogs: process.env.NODE_ENV === 'development',
  }),
  emailAndPassword: {
    enabled: true,
  },
  user: {
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url, token }, request) => {
        // For now, we'll log the verification details
        // In production, you should implement email sending here
        console.log('Email change verification:', {
          currentEmail: user.email,
          newEmail,
          verificationUrl: url,
          token
        });

        // TODO: Implement email sending service
        // Example: await sendEmail({
        //   to: user.email,
        //   subject: 'Approve email change',
        //   html: `Click <a href="${url}">here</a> to approve the email change to ${newEmail}`
        // });
      }
    },
    deleteUser: {
      enabled: true,
      sendDeleteAccountVerification: async ({ user, url, token }, request) => {
        // For now, we'll log the verification details
        // In production, you should implement email sending here
        console.log('Account deletion verification:', {
          userEmail: user.email,
          verificationUrl: url,
          token
        });

        // TODO: Implement email sending service
        // Example: await sendEmail({
        //   to: user.email,
        //   subject: 'Confirm account deletion',
        //   html: `Click <a href="${url}">here</a> to permanently delete your account. This action cannot be undone.`
        // });
      },
      beforeDelete: async (user, request) => {
        // Import database operations for cleanup
        const {
          sessionOperations,
          subscriptionOperations
        } = await import('@/lib/database');

        console.log('Starting account deletion cleanup for user:', user.id);

        try {
          // 1. Cancel all active subscriptions with payment provider
          const userSubscriptions = await subscriptionOperations.findByUserId(user.id);
          for (const subscription of userSubscriptions) {
            if (subscription.status === 'active') {
              try {
                // TODO: Cancel with payment provider (Creem)
                // await creem.cancelSubscription({ id: subscription.id });
                console.log(`Would cancel subscription: ${subscription.id}`);
              } catch (error) {
                console.error(`Failed to cancel subscription ${subscription.id}:`, error);
              }
            }
          }

          // 2. Delete all user sessions
          await sessionOperations.deleteByUserId(user.id);
          console.log(`Deleted sessions for user: ${user.id}`);

          // 3. Delete all OAuth accounts
          const { supabaseAdmin } = await import('@/lib/supabase');
          await supabaseAdmin.from('accounts').delete().eq('user_id', user.id);
          console.log(`Deleted OAuth accounts for user: ${user.id}`);

          // 4. Delete all subscriptions
          await supabaseAdmin.from('subscriptions').delete().eq('user_id', user.id);
          console.log(`Deleted subscriptions for user: ${user.id}`);

          // 5. Delete all verifications
          await supabaseAdmin.from('verifications').delete().eq('identifier', user.email);
          console.log(`Deleted verifications for user: ${user.id}`);

          console.log('Account deletion cleanup completed successfully');

        } catch (error) {
          console.error('Error during account deletion cleanup:', error);
          // Don't throw - let the user deletion proceed even if cleanup fails
          // In production, you might want to implement retry logic or manual cleanup
        }
      },
      afterDelete: async (user, request) => {
        // Log successful deletion
        console.log('User account successfully deleted:', {
          userId: user.id,
          email: user.email,
          completedAt: new Date().toISOString()
        });

        // You can add post-deletion logic here
        // Example: Send confirmation email, update analytics, etc.
      }
    }
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
});
