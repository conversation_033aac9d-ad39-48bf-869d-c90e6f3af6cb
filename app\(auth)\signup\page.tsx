"use client";
import { Suspense, useEffect } from "react";
import { Background } from "@/components/creem/landing/background";
import { TerminalButton } from "@/components/ui/terminal-button";
import { useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { motion, AnimatePresence } from "framer-motion";
import { Eye, EyeOff, Mail, Lock, ArrowRight, Github, User, Shield } from "lucide-react";
import Link from "next/link";

export default function SignupPage() {
  const router = useRouter();
  useEffect(() => {
    authClient.getSession().then(({ data }) => {
      if (data?.user) router.replace("/dashboard");
    });
  }, [router]);

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-black overflow-hidden">
      <Background />

      {/* Floating elements for visual enhancement */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-1/3 right-1/4 w-3 h-3 bg-[#ffbe98]/15 rounded-full"
          animate={{
            y: [0, -25, 0],
            opacity: [0.15, 0.6, 0.15],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/3 left-1/3 w-2 h-2 bg-[#ffbe98]/25 rounded-full"
          animate={{
            y: [0, -18, 0],
            opacity: [0.25, 0.8, 0.25],
          }}
          transition={{
            duration: 3.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        />
      </div>

      <main className="relative z-10 w-full max-w-md mx-auto px-4 py-12 flex flex-col items-center">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center mb-8"
        >
          <motion.h1
            className="font-mono text-2xl md:text-3xl text-white mb-2 select-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <span className="text-[#ffbe98]">$</span> Join Creem
          </motion.h1>
          <motion.p
            className="text-neutral-400 text-sm font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            Create your developer account
          </motion.p>
        </motion.div>

        <Suspense
          fallback={
            <motion.div
              className="text-neutral-400 font-mono flex items-center gap-2"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <div className="w-2 h-2 bg-[#ffbe98] rounded-full animate-pulse" />
              Loading...
            </motion.div>
          }
        >
          <SignupForm />
        </Suspense>

        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <p className="text-neutral-500 text-sm font-mono">
            Already have an account?{" "}
            <Link
              href="/signin"
              className="text-[#ffbe98] hover:text-[#ffbe98]/80 transition-colors underline underline-offset-2"
            >
              Sign in
            </Link>
          </p>
        </motion.div>
      </main>
    </div>
  );
}

import { useState, FormEvent } from "react";

interface SignupFormState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
}

function SignupForm() {
  const [form, setForm] = useState({ email: "", password: "", confirmPassword: "" });
  const [state, setState] = useState<SignupFormState>({
    isLoading: false,
    hasError: false,
    errorMessage: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [consents, setConsents] = useState({
    privacyPolicy: false,
    termsOfService: false,
    marketingEmails: false,
  });
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const router = useRouter();

  // Password strength calculation
  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    return strength;
  };

  const getPasswordStrengthColor = (strength: number) => {
    if (strength <= 1) return "bg-red-500";
    if (strength <= 2) return "bg-orange-500";
    if (strength <= 3) return "bg-yellow-500";
    if (strength <= 4) return "bg-blue-500";
    return "bg-green-500";
  };

  const getPasswordStrengthText = (strength: number) => {
    if (strength <= 1) return "Weak";
    if (strength <= 2) return "Fair";
    if (strength <= 3) return "Good";
    if (strength <= 4) return "Strong";
    return "Very Strong";
  };

  async function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    // Validation
    if (form.password !== form.confirmPassword) {
      setState({
        isLoading: false,
        hasError: true,
        errorMessage: "Passwords do not match",
      });
      return;
    }

    if (passwordStrength < 3) {
      setState({
        isLoading: false,
        hasError: true,
        errorMessage: "Password is too weak. Please use a stronger password.",
      });
      return;
    }

    setState({ isLoading: true, hasError: false, errorMessage: "" });
    try {
      const email = form.email;
      const password = form.password;
      const { data, error } = await authClient.signUp.email(
        {
          email,
          password,
          callbackURL: `/dashboard`,
          name: form.email,
        },
        {
          onRequest: (ctx) => {
            console.log("Requesting signup", ctx);
          },
          onSuccess: (ctx) => {
            console.log("Signup successful", ctx);
          },
          onError: (ctx) => {
            console.error("Signup error", ctx);
            setState({
              isLoading: false,
              hasError: true,
              errorMessage: ctx.error.message,
            });
          },
        },
      );
      if (error) throw error;

      // Save GDPR consents after successful signup
      try {
        await fetch('/api/gdpr/consent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            consents: {
              privacy_policy: consents.privacyPolicy,
              terms_of_service: consents.termsOfService,
              marketing_emails: consents.marketingEmails,
            }
          }),
        });
      } catch (consentError) {
        console.error('Failed to save consent preferences:', consentError);
        // Don't fail signup if consent saving fails
      }

      // After signup, ensure user is logged in and redirect to /dashboard
      window.location.href = "/dashboard";
    } catch (err: any) {
      setState({
        isLoading: false,
        hasError: true,
        errorMessage: err?.message || "Signup failed",
      });
    }
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { name, value } = e.target;
    setForm((f) => ({ ...f, [name]: value }));

    if (name === "password") {
      setPasswordStrength(calculatePasswordStrength(value));
    }
  }

  return (
    <motion.form
      onSubmit={handleSubmit}
      className="w-full flex flex-col gap-6 bg-neutral-900/90 border border-neutral-800/50 rounded-xl p-8 shadow-2xl backdrop-blur-sm"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      {/* Email Field */}
      <motion.div
        className="space-y-2"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <label className="font-mono text-sm text-neutral-300 flex items-center gap-2" htmlFor="email">
          <Mail className="w-4 h-4 text-[#ffbe98]" />
          Email
        </label>
        <div className="relative">
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            className={`w-full rounded-lg bg-neutral-950/80 border px-4 py-3 text-white font-mono
              transition-all duration-200 placeholder:text-neutral-500
              ${focusedField === 'email'
                ? 'border-[#ffbe98] ring-2 ring-[#ffbe98]/20 bg-neutral-950'
                : 'border-neutral-800 hover:border-neutral-700'
              }
              focus:outline-none focus:border-[#ffbe98] focus:ring-2 focus:ring-[#ffbe98]/20`}
            placeholder="<EMAIL>"
            value={form.email}
            onChange={handleChange}
            onFocus={() => setFocusedField('email')}
            onBlur={() => setFocusedField(null)}
            disabled={state.isLoading}
          />
          {form.email && (
            <motion.div
              className="absolute right-3 top-1/2 -translate-y-1/2"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-2 h-2 bg-green-500 rounded-full" />
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Password Field */}
      <motion.div
        className="space-y-2"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <label className="font-mono text-sm text-neutral-300 flex items-center gap-2" htmlFor="password">
          <Lock className="w-4 h-4 text-[#ffbe98]" />
          Password
        </label>
        <div className="relative">
          <input
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
            required
            minLength={8}
            className={`w-full rounded-lg bg-neutral-950/80 border px-4 py-3 pr-12 text-white font-mono
              transition-all duration-200 placeholder:text-neutral-500
              ${focusedField === 'password'
                ? 'border-[#ffbe98] ring-2 ring-[#ffbe98]/20 bg-neutral-950'
                : 'border-neutral-800 hover:border-neutral-700'
              }
              focus:outline-none focus:border-[#ffbe98] focus:ring-2 focus:ring-[#ffbe98]/20`}
            placeholder="••••••••"
            value={form.password}
            onChange={handleChange}
            onFocus={() => setFocusedField('password')}
            onBlur={() => setFocusedField(null)}
            disabled={state.isLoading}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-300 transition-colors"
            disabled={state.isLoading}
          >
            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        </div>

        {/* Password Strength Indicator */}
        {form.password && (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                <motion.div
                  className={`h-full ${getPasswordStrengthColor(passwordStrength)} transition-all duration-300`}
                  initial={{ width: 0 }}
                  animate={{ width: `${(passwordStrength / 5) * 100}%` }}
                />
              </div>
              <span className={`text-xs font-mono ${passwordStrength <= 2 ? 'text-red-400' : passwordStrength <= 3 ? 'text-yellow-400' : 'text-green-400'}`}>
                {getPasswordStrengthText(passwordStrength)}
              </span>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Confirm Password Field */}
      <motion.div
        className="space-y-2"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <label className="font-mono text-sm text-neutral-300 flex items-center gap-2" htmlFor="confirmPassword">
          <Shield className="w-4 h-4 text-[#ffbe98]" />
          Confirm Password
        </label>
        <div className="relative">
          <input
            id="confirmPassword"
            name="confirmPassword"
            type={showConfirmPassword ? "text" : "password"}
            autoComplete="new-password"
            required
            className={`w-full rounded-lg bg-neutral-950/80 border px-4 py-3 pr-12 text-white font-mono
              transition-all duration-200 placeholder:text-neutral-500
              ${focusedField === 'confirmPassword'
                ? 'border-[#ffbe98] ring-2 ring-[#ffbe98]/20 bg-neutral-950'
                : 'border-neutral-800 hover:border-neutral-700'
              }
              ${form.confirmPassword && form.password !== form.confirmPassword ? 'border-red-500' : ''}
              focus:outline-none focus:border-[#ffbe98] focus:ring-2 focus:ring-[#ffbe98]/20`}
            placeholder="••••••••"
            value={form.confirmPassword}
            onChange={handleChange}
            onFocus={() => setFocusedField('confirmPassword')}
            onBlur={() => setFocusedField(null)}
            disabled={state.isLoading}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-300 transition-colors"
            disabled={state.isLoading}
          >
            {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        </div>

        {/* Password Match Indicator */}
        {form.confirmPassword && (
          <motion.div
            className="flex items-center gap-2 text-xs font-mono"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {form.password === form.confirmPassword ? (
              <>
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span className="text-green-400">Passwords match</span>
              </>
            ) : (
              <>
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span className="text-red-400">Passwords do not match</span>
              </>
            )}
          </motion.div>
        )}
      </motion.div>

      {/* GDPR Consent Checkboxes */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="space-y-3 mt-6"
      >
        <div className="space-y-3">
          {/* Privacy Policy Consent (Required) */}
          <label className="flex items-start gap-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={consents.privacyPolicy}
              onChange={(e) => setConsents(prev => ({ ...prev, privacyPolicy: e.target.checked }))}
              className="mt-1 w-4 h-4 text-[#ffbe98] bg-neutral-800 border-neutral-600 rounded focus:ring-[#ffbe98] focus:ring-2"
              required
            />
            <span className="text-sm text-neutral-300 group-hover:text-white transition-colors">
              I agree to the{' '}
              <Link href="/privacy" className="text-[#ffbe98] hover:underline" target="_blank">
                Privacy Policy
              </Link>{' '}
              and understand how my data will be processed. <span className="text-red-400">*</span>
            </span>
          </label>

          {/* Terms of Service Consent (Required) */}
          <label className="flex items-start gap-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={consents.termsOfService}
              onChange={(e) => setConsents(prev => ({ ...prev, termsOfService: e.target.checked }))}
              className="mt-1 w-4 h-4 text-[#ffbe98] bg-neutral-800 border-neutral-600 rounded focus:ring-[#ffbe98] focus:ring-2"
              required
            />
            <span className="text-sm text-neutral-300 group-hover:text-white transition-colors">
              I accept the{' '}
              <Link href="/terms" className="text-[#ffbe98] hover:underline" target="_blank">
                Terms of Service
              </Link>. <span className="text-red-400">*</span>
            </span>
          </label>

          {/* Marketing Emails Consent (Optional) */}
          <label className="flex items-start gap-3 cursor-pointer group">
            <input
              type="checkbox"
              checked={consents.marketingEmails}
              onChange={(e) => setConsents(prev => ({ ...prev, marketingEmails: e.target.checked }))}
              className="mt-1 w-4 h-4 text-[#ffbe98] bg-neutral-800 border-neutral-600 rounded focus:ring-[#ffbe98] focus:ring-2"
            />
            <span className="text-sm text-neutral-300 group-hover:text-white transition-colors">
              I would like to receive marketing emails and product updates (optional)
            </span>
          </label>
        </div>

        <p className="text-xs text-neutral-500 mt-2">
          <span className="text-red-400">*</span> Required fields. You can withdraw consent at any time in your account settings.
        </p>
      </motion.div>

      {/* Submit Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <TerminalButton
          type="submit"
          className="mt-2 w-full justify-center bg-[#ffbe98]/10 border-[#ffbe98]/30 hover:bg-[#ffbe98]/20 hover:border-[#ffbe98]/50 transition-all duration-200"
          disabled={
            state.isLoading ||
            form.password !== form.confirmPassword ||
            passwordStrength < 3 ||
            !consents.privacyPolicy ||
            !consents.termsOfService
          }
          prompt={"$"}
          command={state.isLoading ? "creating account..." : "signup"}
          path={form.email ? form.email.split('@')[0] : "/user"}
        >
          <div className="flex items-center gap-2">
            {state.isLoading ? (
              <motion.div
                className="w-4 h-4 border-2 border-[#ffbe98] border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            ) : (
              <User className="w-4 h-4" />
            )}
            {state.isLoading ? "Creating account..." : "Create account"}
          </div>
        </TerminalButton>
      </motion.div>

      {/* Error Message */}
      <AnimatePresence>
        {state.hasError && (
          <motion.div
            className="bg-red-950/20 border border-red-800/50 rounded-lg p-3 space-y-3"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-red-400 font-mono text-xs flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0" />
              {state.errorMessage}
            </div>
            {state.errorMessage?.toLowerCase().includes("already exists") && (
              <motion.button
                type="button"
                className="w-full rounded-lg bg-neutral-800/50 border border-neutral-700 text-white font-mono py-2 px-4 hover:bg-neutral-800 transition-all duration-200 text-sm"
                onClick={() => router.push("/signin")}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Go to Sign In
              </motion.button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Divider */}
      <motion.div
        className="relative my-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-neutral-800" />
        </div>
        <div className="relative flex justify-center text-xs">
          <span className="bg-neutral-900 px-2 text-neutral-500 font-mono">or continue with</span>
        </div>
      </motion.div>

      {/* Social Login */}
      <motion.button
        type="button"
        className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-neutral-800/50 border border-neutral-700 rounded-lg text-white font-mono text-sm hover:bg-neutral-800 transition-all duration-200 group"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        disabled={state.isLoading}
      >
        <Github className="w-4 h-4 group-hover:scale-110 transition-transform" />
        Continue with GitHub
      </motion.button>
    </motion.form>
  );
}
