import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * GET /api/gdpr/consent
 * 
 * Get user's current consent preferences
 * GDPR Article 7 - Conditions for consent
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get all current consents for the user
    const { data: consents, error } = await supabaseAdmin
      .from('user_consents')
      .select('*')
      .eq('user_id', userId)
      .is('withdrawal_date', null) // Only active consents
      .order('consent_date', { ascending: false });

    if (error) {
      throw error;
    }

    // Group consents by type for easier frontend consumption
    const consentMap = consents?.reduce((acc: any, consent: any) => {
      acc[consent.consent_type] = {
        given: consent.consent_given,
        date: consent.consent_date,
        id: consent.id
      };
      return acc;
    }, {}) || {};

    return NextResponse.json({
      user_id: userId,
      consents: consentMap,
      consent_history: consents || []
    });

  } catch (error: any) {
    console.error('Get consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/gdpr/consent
 * 
 * Update user consent preferences
 * GDPR Article 7 - Conditions for consent
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { consents } = body; // Object with consent_type: boolean pairs

    if (!consents || typeof consents !== 'object') {
      return NextResponse.json(
        { error: "Invalid consent data" },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Process each consent update
    const consentUpdates = [];
    const auditLogs = [];

    for (const [consentType, consentGiven] of Object.entries(consents)) {
      if (typeof consentGiven !== 'boolean') {
        continue;
      }

      // Insert new consent record
      const consentRecord = {
        user_id: userId,
        consent_type: consentType,
        consent_given: consentGiven,
        consent_date: new Date().toISOString(),
        ip_address: clientIP,
        user_agent: userAgent
      };

      consentUpdates.push(consentRecord);

      // Create audit log entry
      auditLogs.push({
        user_id: userId,
        action: 'consent_change',
        resource: 'user_consents',
        details: {
          consent_type: consentType,
          consent_given: consentGiven,
          previous_consent: null // Could be enhanced to track previous state
        },
        ip_address: clientIP,
        user_agent: userAgent
      });
    }

    // Withdraw previous consents of the same types
    const consentTypes = Object.keys(consents);
    if (consentTypes.length > 0) {
      await supabaseAdmin
        .from('user_consents')
        .update({ withdrawal_date: new Date().toISOString() })
        .eq('user_id', userId)
        .in('consent_type', consentTypes)
        .is('withdrawal_date', null);
    }

    // Insert new consent records
    if (consentUpdates.length > 0) {
      const { error: consentError } = await supabaseAdmin
        .from('user_consents')
        .insert(consentUpdates);

      if (consentError) {
        throw consentError;
      }
    }

    // Insert audit logs
    if (auditLogs.length > 0) {
      await supabaseAdmin
        .from('audit_logs')
        .insert(auditLogs);
    }

    console.log(`Consent updated for user ${userId}:`, consents);

    return NextResponse.json({
      success: true,
      message: "Consent preferences updated successfully",
      updated_consents: consents
    });

  } catch (error: any) {
    console.error('Update consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/gdpr/consent
 * 
 * Withdraw all consents (GDPR Article 7.3)
 * User has the right to withdraw consent at any time
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Withdraw all active consents
    const { error } = await supabaseAdmin
      .from('user_consents')
      .update({ withdrawal_date: new Date().toISOString() })
      .eq('user_id', userId)
      .is('withdrawal_date', null);

    if (error) {
      throw error;
    }

    // Log the consent withdrawal
    await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'consent_withdrawal_all',
        resource: 'user_consents',
        details: {
          action: 'withdrew_all_consents',
          reason: 'user_requested'
        },
        ip_address: clientIP,
        user_agent: userAgent
      });

    console.log(`All consents withdrawn for user: ${userId}`);

    return NextResponse.json({
      success: true,
      message: "All consents have been withdrawn successfully"
    });

  } catch (error: any) {
    console.error('Withdraw consent error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
