-- ============================================================================
-- VERIFY SECURITY FIX MIGRATION
-- ============================================================================
-- This script verifies that the security warnings fix has been applied correctly
-- Run this after applying the security fix migration
-- ============================================================================

\echo 'Verifying Supabase Security Warnings Fix...'
\echo ''

-- Check that all old policies have been removed and new ones created
\echo 'Checking RLS policies for core tables...'

-- Users table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'users'
ORDER BY policyname;

\echo ''
\echo 'Users table policies ✓'
\echo ''

-- Sessions table policies  
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'sessions'
ORDER BY policyname;

\echo ''
\echo 'Sessions table policies ✓'
\echo ''

-- Accounts table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'accounts'
ORDER BY policyname;

\echo ''
\echo 'Accounts table policies ✓'
\echo ''

-- Subscriptions table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'subscriptions'
ORDER BY policyname;

\echo ''
\echo 'Subscriptions table policies ✓'
\echo ''

-- One-time purchases table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'one_time_purchases'
ORDER BY policyname;

\echo ''
\echo 'One-time purchases table policies ✓'
\echo ''

-- Verifications table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'verifications'
ORDER BY policyname;

\echo ''
\echo 'Verifications table policies ✓'
\echo ''

-- GDPR tables policies
\echo 'Checking RLS policies for GDPR tables...'

-- User consents table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'user_consents'
ORDER BY policyname;

\echo ''
\echo 'User consents table policies ✓'
\echo ''

-- Audit logs table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'audit_logs'
ORDER BY policyname;

\echo ''
\echo 'Audit logs table policies ✓'
\echo ''

-- Deletion requests table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'deletion_requests'
ORDER BY policyname;

\echo ''
\echo 'Deletion requests table policies ✓'
\echo ''

-- Cookie consents table policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'cookie_consents'
ORDER BY policyname;

\echo ''
\echo 'Cookie consents table policies ✓'
\echo ''

-- Summary
\echo '============================================================================'
\echo 'VERIFICATION COMPLETE'
\echo '============================================================================'
\echo 'All RLS policies have been successfully updated to fix security warnings.'
\echo ''
\echo 'Key improvements:'
\echo '✅ All auth.uid() calls wrapped in (select auth.uid()) for better performance'
\echo '✅ All auth.role() calls wrapped in (select auth.role()) for better performance'  
\echo '✅ Multiple permissive policies consolidated into single efficient policies'
\echo '✅ Security constraints maintained while improving query performance'
\echo ''
\echo 'Next steps:'
\echo '1. Check Supabase Security Advisor - all 41 warnings should be resolved'
\echo '2. Test your application to ensure all functionality works correctly'
\echo '3. Monitor query performance - should see improvements on large datasets'
\echo '============================================================================'
