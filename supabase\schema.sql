-- Supabase Schema Migration
-- Generated from Prisma schema for SaaS Boilerplate
-- This script creates all necessary tables with proper constraints, indexes, and RLS policies

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better type safety (only if they don't exist)
DO $$ BEGIN
    CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'expired', 'past_due');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Users table (core authentication table)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    email_verified BOOLEAN NOT NULL DEFAULT false,
    image TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Sessions table (for better-auth session management)
CREATE TABLE IF NOT EXISTS public.sessions (
    id TEXT PRIMARY KEY,
    expires_at TIMESTAMPTZ NOT NULL,
    token TEXT UNIQUE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ip_address TEXT,
    user_agent TEXT,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE
);

-- Accounts table (for OAuth and external authentication providers)
CREATE TABLE IF NOT EXISTS public.accounts (
    id TEXT PRIMARY KEY,
    account_id TEXT NOT NULL,
    provider_id TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    access_token TEXT,
    refresh_token TEXT,
    id_token TEXT,
    access_token_expires_at TIMESTAMPTZ,
    refresh_token_expires_at TIMESTAMPTZ,
    scope TEXT,
    password TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Subscriptions table (recurring payments and subscriptions)
CREATE TABLE IF NOT EXISTS public.subscriptions (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    product TEXT NOT NULL,
    provider_customer_id TEXT NOT NULL,
    status subscription_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- One-time purchases table (single payments)
CREATE TABLE IF NOT EXISTS public.one_time_purchases (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    product TEXT NOT NULL,
    provider_customer_id TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Verification table (for email verification, password reset, etc.)
CREATE TABLE IF NOT EXISTS public.verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier TEXT NOT NULL,
    value TEXT NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON public.sessions(token);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON public.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON public.accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_one_time_purchases_user_id ON public.one_time_purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_verifications_identifier ON public.verifications(identifier);
CREATE INDEX IF NOT EXISTS idx_verifications_expires_at ON public.verifications(expires_at);

-- Create updated_at trigger function with secure search_path
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Apply updated_at triggers to relevant tables (only if they don't exist)
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_sessions_updated_at ON public.sessions;
CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON public.sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_accounts_updated_at ON public.accounts;
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON public.accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_one_time_purchases_updated_at ON public.one_time_purchases;
CREATE TRIGGER update_one_time_purchases_updated_at BEFORE UPDATE ON public.one_time_purchases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_verifications_updated_at ON public.verifications;
CREATE TRIGGER update_verifications_updated_at BEFORE UPDATE ON public.verifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.one_time_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.verifications ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- OPTIMIZED RLS POLICIES - PERFORMANCE FOCUSED
-- ============================================================================
-- These policies are optimized to fix Supabase security warnings:
-- 1. Use (select auth.uid()) instead of auth.uid() for better performance
-- 2. Consolidate multiple permissive policies into single efficient policies
-- 3. Maintain security while improving query performance

-- RLS Policies for users table - Consolidated for performance
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Service role can manage all users" ON public.users;

-- Single consolidated policy for users SELECT operations
CREATE POLICY "Users and service can view profiles" ON public.users
    FOR SELECT USING (
        (select auth.uid()) = id OR
        (select auth.role()) = 'service_role'
    );

-- Single consolidated policy for users UPDATE operations
CREATE POLICY "Users and service can update profiles" ON public.users
    FOR UPDATE USING (
        (select auth.uid()) = id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can INSERT and DELETE users
CREATE POLICY "Service role can manage user lifecycle" ON public.users
    FOR ALL USING ((select auth.role()) = 'service_role');

-- RLS Policies for sessions table - Consolidated for performance
DROP POLICY IF EXISTS "Users can view own sessions" ON public.sessions;
DROP POLICY IF EXISTS "Users can delete own sessions" ON public.sessions;
DROP POLICY IF EXISTS "Service role can manage all sessions" ON public.sessions;

-- Single consolidated policy for sessions SELECT operations
CREATE POLICY "Users and service can view sessions" ON public.sessions
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Single consolidated policy for sessions DELETE operations
CREATE POLICY "Users and service can delete sessions" ON public.sessions
    FOR DELETE USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can INSERT and UPDATE sessions
CREATE POLICY "Service role can manage session lifecycle" ON public.sessions
    FOR ALL USING ((select auth.role()) = 'service_role');

-- RLS Policies for accounts table - Consolidated for performance
DROP POLICY IF EXISTS "Users can view own accounts" ON public.accounts;
DROP POLICY IF EXISTS "Service role can manage all accounts" ON public.accounts;

-- Single consolidated policy for accounts SELECT operations
CREATE POLICY "Users and service can view accounts" ON public.accounts
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all account operations
CREATE POLICY "Service role can manage all accounts" ON public.accounts
    FOR ALL USING ((select auth.role()) = 'service_role');

-- RLS Policies for subscriptions table - Consolidated for performance
DROP POLICY IF EXISTS "Users can view own subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Service role can manage all subscriptions" ON public.subscriptions;

-- Single consolidated policy for subscriptions SELECT operations
CREATE POLICY "Users and service can view subscriptions" ON public.subscriptions
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all subscription operations
CREATE POLICY "Service role can manage all subscriptions" ON public.subscriptions
    FOR ALL USING ((select auth.role()) = 'service_role');

-- RLS Policies for one_time_purchases table - Consolidated for performance
DROP POLICY IF EXISTS "Users can view own purchases" ON public.one_time_purchases;
DROP POLICY IF EXISTS "Service role can manage all purchases" ON public.one_time_purchases;

-- Single consolidated policy for purchases SELECT operations
CREATE POLICY "Users and service can view purchases" ON public.one_time_purchases
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all purchase operations
CREATE POLICY "Service role can manage all purchases" ON public.one_time_purchases
    FOR ALL USING ((select auth.role()) = 'service_role');

-- RLS Policies for verifications table - Service role only
DROP POLICY IF EXISTS "Service role can manage all verifications" ON public.verifications;

-- Verifications are managed by service role only for security
CREATE POLICY "Service role can manage all verifications" ON public.verifications
    FOR ALL USING ((select auth.role()) = 'service_role');

-- ============================================================================
-- GDPR COMPLIANCE TABLES
-- ============================================================================

-- User consent tracking (GDPR Article 7)
CREATE TABLE IF NOT EXISTS public.user_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    consent_type TEXT NOT NULL, -- 'privacy_policy', 'marketing_emails', 'analytics', etc.
    consent_given BOOLEAN NOT NULL,
    consent_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    withdrawal_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Audit logs for data access (GDPR Article 30)
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL, -- 'data_access', 'data_export', 'data_deletion', 'consent_change'
    resource TEXT NOT NULL, -- 'user_profile', 'subscriptions', 'purchases'
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data deletion requests tracking (GDPR Article 17)
CREATE TABLE IF NOT EXISTS public.deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    request_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    completion_date TIMESTAMPTZ,
    deletion_method TEXT, -- 'hard_delete', 'anonymization'
    ip_address INET,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cookie consent tracking
CREATE TABLE IF NOT EXISTS public.cookie_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    session_id TEXT, -- For anonymous users
    necessary_cookies BOOLEAN NOT NULL DEFAULT true,
    analytics_cookies BOOLEAN NOT NULL DEFAULT false,
    marketing_cookies BOOLEAN NOT NULL DEFAULT false,
    consent_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- GDPR table indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_consents_user_id ON public.user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_type ON public.user_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_deletion_requests_user_id ON public.deletion_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_deletion_requests_status ON public.deletion_requests(status);
CREATE INDEX IF NOT EXISTS idx_cookie_consents_user_id ON public.cookie_consents(user_id);

-- Enable RLS on GDPR tables
ALTER TABLE public.user_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deletion_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cookie_consents ENABLE ROW LEVEL SECURITY;

-- GDPR table triggers for updated_at
DROP TRIGGER IF EXISTS update_user_consents_updated_at ON public.user_consents;
CREATE TRIGGER update_user_consents_updated_at BEFORE UPDATE ON public.user_consents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_deletion_requests_updated_at ON public.deletion_requests;
CREATE TRIGGER update_deletion_requests_updated_at BEFORE UPDATE ON public.deletion_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_cookie_consents_updated_at ON public.cookie_consents;
CREATE TRIGGER update_cookie_consents_updated_at BEFORE UPDATE ON public.cookie_consents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- OPTIMIZED GDPR RLS POLICIES - PERFORMANCE FOCUSED
-- ============================================================================
-- These policies are optimized to fix Supabase security warnings for GDPR tables

-- User consents: Consolidated policies for performance
DROP POLICY IF EXISTS "Users can view own consents" ON public.user_consents;
DROP POLICY IF EXISTS "Users can insert own consents" ON public.user_consents;
DROP POLICY IF EXISTS "Service role can manage all consents" ON public.user_consents;

-- Single consolidated policy for user consents SELECT operations
CREATE POLICY "Users and service can view consents" ON public.user_consents
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Single consolidated policy for user consents INSERT operations
CREATE POLICY "Users and service can insert consents" ON public.user_consents
    FOR INSERT WITH CHECK (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all consent operations
CREATE POLICY "Service role can manage all consents" ON public.user_consents
    FOR ALL USING ((select auth.role()) = 'service_role');

-- Audit logs: Consolidated policies for performance
DROP POLICY IF EXISTS "Users can view own audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Service role can manage all audit logs" ON public.audit_logs;

-- Single consolidated policy for audit logs SELECT operations
CREATE POLICY "Users and service can view audit logs" ON public.audit_logs
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all audit log operations
CREATE POLICY "Service role can manage all audit logs" ON public.audit_logs
    FOR ALL USING ((select auth.role()) = 'service_role');

-- Deletion requests: Consolidated policies for performance
DROP POLICY IF EXISTS "Users can view own deletion requests" ON public.deletion_requests;
DROP POLICY IF EXISTS "Users can create own deletion requests" ON public.deletion_requests;
DROP POLICY IF EXISTS "Service role can manage all deletion requests" ON public.deletion_requests;

-- Single consolidated policy for deletion requests SELECT operations
CREATE POLICY "Users and service can view deletion requests" ON public.deletion_requests
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Single consolidated policy for deletion requests INSERT operations
CREATE POLICY "Users and service can create deletion requests" ON public.deletion_requests
    FOR INSERT WITH CHECK (
        (select auth.uid()) = user_id OR
        (select auth.role()) = 'service_role'
    );

-- Service role can manage all deletion request operations
CREATE POLICY "Service role can manage all deletion requests" ON public.deletion_requests
    FOR ALL USING ((select auth.role()) = 'service_role');

-- Cookie consents: Consolidated policies for performance (supports anonymous users)
DROP POLICY IF EXISTS "Users can view own cookie consents" ON public.cookie_consents;
DROP POLICY IF EXISTS "Users can manage own cookie consents" ON public.cookie_consents;
DROP POLICY IF EXISTS "Service role can manage all cookie consents" ON public.cookie_consents;

-- Single consolidated policy for cookie consents SELECT operations
CREATE POLICY "Users and service can view cookie consents" ON public.cookie_consents
    FOR SELECT USING (
        (select auth.uid()) = user_id OR
        user_id IS NULL OR
        (select auth.role()) = 'service_role'
    );

-- Single consolidated policy for cookie consents management (all operations)
CREATE POLICY "Users and service can manage cookie consents" ON public.cookie_consents
    FOR ALL USING (
        (select auth.uid()) = user_id OR
        user_id IS NULL OR
        (select auth.role()) = 'service_role'
    );
