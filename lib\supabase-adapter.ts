/**
 * Pure Supabase Better Auth Adapter
 * 
 * A custom database adapter that integrates Better Auth directly with Supabase
 * using the Supabase client instead of DATABASE_URL or PostgreSQL connections.
 * 
 * Features:
 * - Direct Supabase client integration using service role
 * - Automatic field mapping between Better Auth and Supabase schema
 * - Type-safe database operations
 * - Proper error handling and debugging
 * - Support for all Better Auth database operations
 */

import { createAdapter, type AdapterDebugLogs } from "better-auth/adapters";
import { supabaseAdmin } from "@/lib/supabase";
import type { SupabaseClient } from "@supabase/supabase-js";

/**
 * Configuration options for the Supabase adapter
 */
interface SupabaseAdapterConfig {
  /**
   * Enable debug logging for development and troubleshooting
   */
  debugLogs?: AdapterDebugLogs;
  /**
   * Whether table names in the schema are plural (default: true for Supabase)
   */
  usePlural?: boolean;
  /**
   * Custom Supabase client (defaults to supabaseAdmin)
   */
  client?: SupabaseClient;
}

/**
 * Field mapping from Better Auth camelCase to Supabase snake_case
 * This ensures compatibility between Better Auth's expected field names
 * and Supabase's snake_case convention
 */
const FIELD_MAPPING_INPUT: Record<string, string> = {
  userId: "user_id",
  createdAt: "created_at",
  updatedAt: "updated_at",
  expiresAt: "expires_at",
  emailVerified: "email_verified",
  providerId: "provider_id",
  accountId: "account_id",
  accessToken: "access_token",
  refreshToken: "refresh_token",
  idToken: "id_token",
  accessTokenExpiresAt: "access_token_expires_at",
  refreshTokenExpiresAt: "refresh_token_expires_at",
  ipAddress: "ip_address",
  userAgent: "user_agent",
  providerCustomerId: "provider_customer_id",
};

/**
 * Reverse field mapping from Supabase snake_case to Better Auth camelCase
 */
const FIELD_MAPPING_OUTPUT: Record<string, string> = Object.fromEntries(
  Object.entries(FIELD_MAPPING_INPUT).map(([key, value]) => [value, key])
);

/**
 * Table name mapping for Better Auth models to Supabase tables
 */
const TABLE_MAPPING: Record<string, string> = {
  user: "users",
  session: "sessions", 
  account: "accounts",
  verification: "verifications",
};

/**
 * Transform object keys from camelCase to snake_case for database input
 * Also handles ID generation for UUID fields
 */
function transformKeysForInput(obj: Record<string, any>, tableName?: string): Record<string, any> {
  if (!obj || typeof obj !== 'object') return obj;

  const transformed: Record<string, any> = {};

  // Tables that use UUID for primary keys (auto-generated by database)
  // Note: 'accounts' and 'sessions' use TEXT IDs provided by Better Auth, not auto-generated UUIDs
  const UUID_TABLES = ['users', 'verifications', 'subscriptions', 'one_time_purchases'];

  console.log(`[transformKeysForInput] Processing ${tableName}:`, { obj, UUID_TABLES });

  for (const [key, value] of Object.entries(obj)) {
    const mappedKey = FIELD_MAPPING_INPUT[key] || key;

    // Handle ID field for UUID tables - let Supabase generate UUID if not valid UUID or null
    if (key === 'id' && tableName && UUID_TABLES.includes(tableName)) {
      if (value === null || value === undefined) {
        console.log(`[transformKeysForInput] Skipping null/undefined UUID ID for ${tableName} - will use database default`);
        continue;
      }

      // If Better Auth provides a non-UUID ID (like for verifications), skip it and let database generate UUID
      if (typeof value === 'string' && !isValidUUID(value)) {
        console.log(`[transformKeysForInput] Skipping non-UUID ID for ${tableName} - will use database default:`, {
          key,
          value,
          type: typeof value,
          isValidUUID: false
        });
        continue;
      }
    }

    // For non-UUID tables (like accounts, sessions), always pass through the ID if provided
    if (key === 'id' && tableName && !UUID_TABLES.includes(tableName)) {
      console.log(`[transformKeysForInput] Passing through ID for non-UUID table ${tableName}:`, {
        key,
        value,
        type: typeof value
      });
    }

    console.log(`[transformKeysForInput] Mapping ${key} -> ${mappedKey}:`, { value });
    transformed[mappedKey] = value;
  }

  console.log(`[transformKeysForInput] Final result for ${tableName}:`, transformed);
  return transformed;
}

/**
 * Check if a string is a valid UUID
 */
function isValidUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * Transform object keys from snake_case to camelCase for output
 */
function transformKeysForOutput(obj: Record<string, any>): Record<string, any> {
  if (!obj || typeof obj !== 'object') return obj;
  
  const transformed: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const mappedKey = FIELD_MAPPING_OUTPUT[key] || key;
    transformed[mappedKey] = value;
  }
  
  return transformed;
}

/**
 * Transform array of objects for output
 */
function transformArrayForOutput(arr: Record<string, any>[]): Record<string, any>[] {
  if (!Array.isArray(arr)) return arr;
  return arr.map(transformKeysForOutput);
}

/**
 * Get the actual table name for a model
 */
function getTableName(model: string): string {
  return TABLE_MAPPING[model] || model;
}

/**
 * Build where clause for Supabase query
 * Handles both object format and Better Auth's array format
 */
function buildWhereClause(query: any, where: any) {
  let result = query;

  // Handle Better Auth's array format: [{ field, operator, value, connector }]
  if (Array.isArray(where)) {
    for (const condition of where) {
      const dbKey = FIELD_MAPPING_INPUT[condition.field] || condition.field;
      const value = condition.value;

      switch (condition.operator) {
        case 'eq':
          if (value === null || value === undefined) {
            result = result.is(dbKey, null);
          } else {
            result = result.eq(dbKey, value);
          }
          break;
        case 'neq':
          result = result.neq(dbKey, value);
          break;
        case 'gt':
          result = result.gt(dbKey, value);
          break;
        case 'gte':
          result = result.gte(dbKey, value);
          break;
        case 'lt':
          result = result.lt(dbKey, value);
          break;
        case 'lte':
          result = result.lte(dbKey, value);
          break;
        case 'in':
          result = result.in(dbKey, value);
          break;
        case 'like':
          result = result.like(dbKey, value);
          break;
        case 'ilike':
          result = result.ilike(dbKey, value);
          break;
        default:
          // Default to eq for unknown operators
          result = result.eq(dbKey, value);
      }
    }
  } else if (where && typeof where === 'object') {
    // Handle object format: { field: value }
    for (const [key, value] of Object.entries(where)) {
      const dbKey = FIELD_MAPPING_INPUT[key] || key;

      if (value === null || value === undefined) {
        result = result.is(dbKey, null);
      } else if (Array.isArray(value)) {
        result = result.in(dbKey, value);
      } else {
        result = result.eq(dbKey, value);
      }
    }
  }

  return result;
}

/**
 * Handle Supabase errors and convert them to appropriate error messages
 */
function handleSupabaseError(error: any, operation: string, model: string): never {
  const message = `Supabase ${operation} operation failed for ${model}: ${error.message}`;
  
  // Handle specific PostgreSQL error codes
  if (error.code === '23505') {
    throw new Error(`Duplicate entry: ${error.message}`);
  }
  
  if (error.code === 'PGRST116') {
    throw new Error(`Record not found`);
  }
  
  if (error.code === '23503') {
    throw new Error(`Foreign key constraint violation: ${error.message}`);
  }
  
  throw new Error(message);
}

/**
 * Create the pure Supabase adapter for Better Auth
 * 
 * This adapter follows the Better Auth adapter specification exactly
 * and provides all required database operations using Supabase client
 */
export const supabaseAdapter = (config: SupabaseAdapterConfig = {}) =>
  createAdapter({
    config: {
      adapterId: "supabase",
      adapterName: "Pure Supabase Adapter",
      usePlural: config.usePlural ?? true, // Supabase typically uses plural table names
      debugLogs: config.debugLogs ?? false,
      supportsJSON: true, // Supabase supports JSON columns
      supportsDates: true, // Supabase supports timestamp columns
      supportsBooleans: true, // Supabase supports boolean columns
      supportsNumericIds: false, // We use UUIDs, not auto-incrementing numbers
    },
    adapter: ({ debugLog }) => {
      const client = config.client || supabaseAdmin;
      
      return {
        create: async ({ model, data, select }) => {
          try {
            const tableName = getTableName(model);

            // Debug the original data before transformation
            debugLog?.(`[DEBUG] Original data for ${model}:`, { originalData: data, tableName });

            const transformedData = transformKeysForInput(data as Record<string, any>, tableName);

            // Debug the transformed data
            debugLog?.(`[DEBUG] Transformed data for ${model}:`, { transformedData, tableName });

            debugLog?.(`Creating ${model} in ${tableName}`, { data: transformedData });

            const selectFields = select && select.length > 0
              ? select.map(field => FIELD_MAPPING_INPUT[field] || field).join(',')
              : '*';

            const query = client.from(tableName as any).insert(transformedData).select(selectFields);

            const { data: result, error } = await query.single();

            if (error) {
              handleSupabaseError(error, 'create', model);
            }

            return transformKeysForOutput(result) as any;
          } catch (error) {
            debugLog?.(`Create ${model} failed:`, error);
            throw error;
          }
        },

        update: async ({ model, where, update }) => {
          try {
            const tableName = getTableName(model);
            const transformedUpdate = transformKeysForInput(update as Record<string, any>, tableName);

            debugLog?.(`Updating ${model} in ${tableName}`, { where, update: transformedUpdate });

            let query = client.from(tableName as any).update(transformedUpdate);
            query = buildWhereClause(query, where);

            const { data: result, error } = await query.select('*').single();

            if (error) {
              handleSupabaseError(error, 'update', model);
            }

            return transformKeysForOutput(result) as any;
          } catch (error) {
            debugLog?.(`Update ${model} failed:`, error);
            throw error;
          }
        },

        updateMany: async ({ model, where, update }) => {
          try {
            const tableName = getTableName(model);
            const transformedUpdate = transformKeysForInput(update as Record<string, any>, tableName);

            debugLog?.(`Updating many ${model} in ${tableName}`, { where, update: transformedUpdate });

            let query = client.from(tableName as any).update(transformedUpdate);
            query = buildWhereClause(query, where);

            const { data: result, error } = await query.select('*');

            if (error) {
              handleSupabaseError(error, 'updateMany', model);
            }

            return transformArrayForOutput(result || []) as any;
          } catch (error) {
            debugLog?.(`UpdateMany ${model} failed:`, error);
            throw error;
          }
        },

        delete: async ({ model, where }) => {
          try {
            const tableName = getTableName(model);
            
            debugLog?.(`Deleting ${model} from ${tableName}`, { where });
            
            let query = client.from(tableName as any).delete();
            query = buildWhereClause(query, where);

            const { error } = await query;
            
            if (error) {
              handleSupabaseError(error, 'delete', model);
            }
          } catch (error) {
            debugLog?.(`Delete ${model} failed:`, error);
            throw error;
          }
        },

        deleteMany: async ({ model, where }) => {
          try {
            const tableName = getTableName(model);
            
            debugLog?.(`Deleting many ${model} from ${tableName}`, { where });
            
            let query = client.from(tableName as any).delete();
            query = buildWhereClause(query, where);

            const { data: result, error } = await query.select('*');
            
            if (error) {
              handleSupabaseError(error, 'deleteMany', model);
            }
            
            return (result || []).length;
          } catch (error) {
            debugLog?.(`DeleteMany ${model} failed:`, error);
            throw error;
          }
        },

        findOne: async ({ model, where, select }) => {
          try {
            const tableName = getTableName(model);

            debugLog?.(`Finding one ${model} in ${tableName}`, { where, select });

            const selectFields = select && select.length > 0
              ? select.map(field => FIELD_MAPPING_INPUT[field] || field).join(',')
              : '*';

            let query = client.from(tableName as any).select(selectFields);
            query = buildWhereClause(query, where);

            const { data: result, error } = await query.single();

            if (error && error.code !== 'PGRST116') {
              handleSupabaseError(error, 'findOne', model);
            }

            const transformedResult = result ? (transformKeysForOutput(result) as any) : null;
            debugLog?.(`FindOne ${model} result:`, { data: transformedResult });

            return transformedResult;
          } catch (error) {
            debugLog?.(`FindOne ${model} failed:`, error);
            throw error;
          }
        },

        findMany: async ({ model, where, limit, offset, sortBy }) => {
          try {
            const tableName = getTableName(model);

            debugLog?.(`Finding many ${model} in ${tableName}`, { where, limit, offset, sortBy });

            let query = client.from(tableName as any).select('*');

            if (where) {
              query = buildWhereClause(query, where);
            }

            if (sortBy && Array.isArray(sortBy)) {
              for (const sort of sortBy) {
                const field = FIELD_MAPPING_INPUT[sort.field] || sort.field;
                query = query.order(field, { ascending: sort.direction === 'asc' });
              }
            }

            if (limit) {
              query = query.limit(limit);
            }

            if (offset) {
              query = query.range(offset, offset + (limit || 1000) - 1);
            }

            const { data: result, error } = await query;

            if (error) {
              handleSupabaseError(error, 'findMany', model);
            }

            return transformArrayForOutput(result || []) as any;
          } catch (error) {
            debugLog?.(`FindMany ${model} failed:`, error);
            throw error;
          }
        },

        count: async ({ model, where }) => {
          try {
            const tableName = getTableName(model);
            
            debugLog?.(`Counting ${model} in ${tableName}`, { where });
            
            let query = client.from(tableName as any).select('*', { count: 'exact', head: true });
            
            if (where) {
              query = buildWhereClause(query, where);
            }
            
            const { count, error } = await query;
            
            if (error) {
              handleSupabaseError(error, 'count', model);
            }
            
            return count || 0;
          } catch (error) {
            debugLog?.(`Count ${model} failed:`, error);
            throw error;
          }
        },

        // Key transformation functions for Better Auth
        mapKeysTransformInput: () => FIELD_MAPPING_INPUT,
        mapKeysTransformOutput: () => FIELD_MAPPING_OUTPUT,
      };
    },
  });
